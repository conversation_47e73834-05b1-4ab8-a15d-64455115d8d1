import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class FirebaseAuthService {
  static final FirebaseAuthService _instance = FirebaseAuthService._internal();
  factory FirebaseAuthService() => _instance;
  FirebaseAuthService._internal();

  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  /// Sign in with Google using Firebase Auth directly
  Future<GoogleSignInResult> signInWithGoogle() async {
    try {
      // Create a new provider
      GoogleAuthProvider googleProvider = GoogleAuthProvider();

      googleProvider.addScope(
        'https://www.googleapis.com/auth/contacts.readonly',
      );
      googleProvider.addScope('email');
      googleProvider.addScope('profile');

      // Sign in with popup for web
      final UserCredential userCredential = await _firebaseAuth.signInWithPopup(
        googleProvider,
      );

      // Get the ID token for backend verification
      final String? idToken = await userCredential.user?.getIdToken();

      return GoogleSignInResult(
        success: true,
        user: userCredential.user,
        idToken: idToken,
        accessToken: null, // Not available with this method
        email: userCredential.user?.email,
        displayName: userCredential.user?.displayName,
        photoUrl: userCredential.user?.photoURL,
      );
    } catch (e) {
      debugPrint('Error signing in with Google: $e');
      return GoogleSignInResult(success: false, error: e.toString());
    }
  }

  /// Sign in with Apple using Firebase Auth
  // Future<GoogleSignInResult> signInWithApple() async {
  //   try {
  //     // Request Apple ID credential
  //     final appleCredential = await SignInWithApple.getAppleIDCredential(
  //       scopes: [
  //         AppleIDAuthorizationScopes.email,
  //         AppleIDAuthorizationScopes.fullName,
  //       ],
  //       webAuthenticationOptions: WebAuthenticationOptions(
  //         clientId: 'com.rm.neorevv', // Replace with your Apple Service ID
  //         redirectUri: Uri.parse(
  //           'https://neorevv-86a08.firebaseapp.com/__/auth/handler',
  //         ),
  //       ),
  //     );

  //     // Create Firebase credential from Apple credential
  //     final oauthCredential = OAuthProvider("apple.com").credential(
  //       idToken: appleCredential.identityToken,
  //       accessToken: appleCredential.authorizationCode,
  //     );

  //     // Sign in with Firebase
  //     final UserCredential userCredential = await _firebaseAuth
  //         .signInWithCredential(oauthCredential);

  //     // Get the ID token for backend verification
  //     final String? idToken = await userCredential.user?.getIdToken();

  //     return GoogleSignInResult(
  //       success: true,
  //       user: userCredential.user,
  //       idToken: idToken,
  //       accessToken: appleCredential.authorizationCode,
  //       email: userCredential.user?.email ?? appleCredential.email,
  //       displayName:
  //           userCredential.user?.displayName ??
  //           '${appleCredential.givenName ?? ''} ${appleCredential.familyName ?? ''}'
  //               .trim(),
  //       photoUrl: userCredential.user?.photoURL,
  //     );
  //   } catch (e) {
  //     debugPrint('Error signing in with Apple: $e');
  //     return GoogleSignInResult(success: false, error: e.toString());
  //   }
  // }

  Future<GoogleSignInResult> signInWithApple() async {
    try {
      print('Requesting Apple ID credential...');
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        webAuthenticationOptions: WebAuthenticationOptions(
          clientId: 'com.rm.neorevv',
          redirectUri: Uri.parse(
            'https://neorevv-86a08.firebaseapp.com/__/auth/handler',
          ),
        ),
      );
      print(
        'Apple Credential: identityToken=${appleCredential.identityToken?.substring(0, 20)}..., '
        'authorizationCode=${appleCredential.authorizationCode?.substring(0, 20)}..., '
        'email=${appleCredential.email}, '
        'givenName=${appleCredential.givenName}, '
        'familyName=${appleCredential.familyName}',
      );

      if (appleCredential.identityToken == null ||
          appleCredential.authorizationCode == null) {
        print('Error: Missing identityToken or authorizationCode');
        return GoogleSignInResult(
          success: false,
          error: 'Missing Apple credential data',
        );
      }

      print('Creating Firebase OAuth credential...');
      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken!,
        accessToken: appleCredential.authorizationCode!,
      );
      print('Firebase OAuth credential created.');

      print('Signing in with Firebase...');
      final UserCredential userCredential = await _firebaseAuth
          .signInWithCredential(oauthCredential);
      print('Firebase sign-in successful. User: ${userCredential.user?.uid}');

      print('Fetching ID token for backend verification...');
      final String? idToken = await userCredential.user?.getIdToken();
      print('ID token retrieved: ${idToken?.substring(0, 20)}...');

      return GoogleSignInResult(
        success: true,
        user: userCredential.user,
        idToken: idToken,
        accessToken: appleCredential.authorizationCode,
        email: userCredential.user?.email ?? appleCredential.email,
        displayName:
            userCredential.user?.displayName ??
            '${appleCredential.givenName ?? ''} ${appleCredential.familyName ?? ''}'
                .trim(),
        photoUrl: userCredential.user?.photoURL,
      );
    } catch (e, stackTrace) {
      debugPrint('Error signing in with Apple: $e');
      print('Error occurred during Apple sign-in: $e\nStackTrace: $stackTrace');
      return GoogleSignInResult(success: false, error: e.toString());
    }
  }

  /// Sign out from Firebase
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
    } catch (e) {
      debugPrint('Error signing out: $e');
      rethrow;
    }
  }

  /// Get current Firebase user
  User? get currentUser => _firebaseAuth.currentUser;

  /// Check if user is signed in
  bool get isSignedIn => _firebaseAuth.currentUser != null;

  /// Get current user's ID token
  Future<String?> getCurrentUserIdToken() async {
    try {
      return await _firebaseAuth.currentUser?.getIdToken();
    } catch (e) {
      debugPrint('Error getting ID token: $e');
      return null;
    }
  }

  /// Listen to auth state changes
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();
}

/// Result class for Google Sign-In operations
class GoogleSignInResult {
  final bool success;
  final String? error;
  final User? user;
  final String? idToken;
  final String? accessToken;
  final String? email;
  final String? displayName;
  final String? photoUrl;

  GoogleSignInResult({
    required this.success,
    this.error,
    this.user,
    this.idToken,
    this.accessToken,
    this.email,
    this.displayName,
    this.photoUrl,
  });

  @override
  String toString() {
    return 'GoogleSignInResult(success: $success, error: $error, email: $email)';
  }
}
