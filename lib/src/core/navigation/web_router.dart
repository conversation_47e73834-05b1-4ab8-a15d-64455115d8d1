import 'package:go_router/go_router.dart';
import 'package:neorevv/src/presentation/screens/auth/login_screen.dart';
import '/main_layout_screen.dart';
import '../../presentation/screens/auth/auth_wrapper.dart';

enum AppRoutes {
  login('/'),
  mainLayout('/home'),
  dashboard('/home/<USER>'),
  brokerages('/home/<USER>'),
  agents('/home/<USER>'),
  sales('/home/<USER>'),
  reports('/home/<USER>'),
  registerBroker('/home/<USER>'),
  registerAgent('/home/<USER>'),
  agentNetwork('/home/<USER>'),
  saleReviewDoc('/home/<USER>');

  const AppRoutes(this.path);
  final String path;
}

final GoRouter appRouter = GoRouter(
  initialLocation: AppRoutes.login.path,
  routes: [
    GoRoute(
      path: AppRoutes.login.path,
      builder: (context, state) => const AuthWrapper(),
    ),
    GoRoute(
      path: '/home/<USER>',
      builder: (context, state) {
        final tab = state.pathParameters['tab'] ?? 'dashboard';
        return MainLayoutScreen(initialTab: tab);
      },
    ),
    GoRoute(
      path: AppRoutes.mainLayout.path,
      redirect: (context, state) => AppRoutes.dashboard.path,
    ),
  ],
);
