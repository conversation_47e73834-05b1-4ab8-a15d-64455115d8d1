import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';

import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '/src/core/config/app_strings.dart';
import '/src/domain/models/login.dart';
import '../../core/network/api_config.dart' show APIConfig;
import '../../domain/repository/auth_repository.dart';
import '../../core/services/exceptions.dart';
import '../../core/network/api_config.dart';
import '../../core/services/firebase_auth_service.dart';
import '../../domain/models/google_auth_result.dart';
import 'package:dio/dio.dart';

class AuthRepositoryImpl extends AuthRepository {
  AuthRepositoryImpl();

  static final String baseUrl = APIConfig.baseUrl;
  static const String loginUrl = APIConfig.login;
  static const String googleSignInUrl = APIConfig.googleSignInVerification;
  static const String appleSignInUrl = APIConfig.appleSignInVerification;
  final FirebaseAuthService _firebaseAuthService = FirebaseAuthService();

  @override
  Future<dynamic> login(Map<String, dynamic> payload) async {
    try {
      final _dio = await DioClient.getDio();
      final response = await _dio.post(loginUrl, data: payload);

      if (response.statusCode == 200) {
        print('LoginModel: ${response.data}');
        return LoginModel.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, loginFailed);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future<dynamic> signInWithGoogle() async {
    try {
      debugPrint('[GoogleSignIn] Starting Google sign-in process...');
      final result = await _firebaseAuthService.signInWithGoogle();
      final _dio = await DioClient.getDio();
      final payload = {'idToken': result.idToken};
      final response = await _dio.post(googleSignInUrl, data: payload);
      if (response.statusCode == 200) {
        return LoginModel.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } catch (e, stackTrace) {
      return GoogleAuthResult.failure(e.toString());
    }
  }

  @override
  Future<dynamic> signInWithApple() async {
    try {
      debugPrint('[AppleSignIn] Starting Apple sign-in process...');
      final result = await _firebaseAuthService.signInWithApple();
      final _dio = await DioClient.getDio();
      final payload = {'idToken': result.idToken};
      final response = await _dio.post(appleSignInUrl, data: payload);
      if (response.statusCode == 200) {
        return LoginModel.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } catch (e) {
      return GoogleAuthResult.failure(e.toString());
    }
  }
  // Future<dynamic> signInWithApple() async {
  //   try {
  //     print('[AppleSignIn] Initiating Apple sign-in process...');
  //     developer.log(
  //       '[AppleSignIn] Starting Apple authentication flow',
  //       name: 'Auth',
  //     );

  //     print('[AppleSignIn] Calling Firebase Auth Service for Apple sign-in...');
  //     developer.log(
  //       '[AppleSignIn] Invoking Firebase Apple sign-in',
  //       name: 'Auth',
  //     );
  //     final result = await _firebaseAuthService.signInWithApple();
  //     print(
  //       '[AppleSignIn] Firebase Apple sign-in successful. Received idToken: ${result.idToken != null ? 'valid' : 'null'}',
  //     );
  //     developer.log(
  //       '[AppleSignIn] Firebase auth completed, idToken present: ${result.idToken != null}',
  //       name: 'Auth',
  //     );

  //     print('[AppleSignIn] Initializing Dio client...');
  //     developer.log('[AppleSignIn] Setting up Dio HTTP client', name: 'Auth');
  //     final _dio = await DioClient.getDio();
  //     print('[AppleSignIn] Dio client initialized successfully.');
  //     developer.log('[AppleSignIn] Dio client ready', name: 'Auth');

  //     final payload = {'idToken': result.idToken};
  //     print(
  //       '[AppleSignIn] Sending POST request to $appleSignInUrl with payload: $payload',
  //     );
  //     developer.log(
  //       '[AppleSignIn] Sending API request with payload: $payload',
  //       name: 'Auth',
  //     );
  //     final response = await _dio.post(appleSignInUrl, data: payload);
  //     print(
  //       '[AppleSignIn] Received response with status code: ${response.statusCode}',
  //     );
  //     developer.log(
  //       '[AppleSignIn] API response received, status: ${response.statusCode}',
  //       name: 'Auth',
  //     );

  //     if (response.statusCode == 200) {
  //       print(
  //         '[AppleSignIn] Successfully authenticated. Parsing response data...',
  //       );
  //       developer.log(
  //         '[AppleSignIn] Authentication successful, parsing response',
  //         name: 'Auth',
  //       );
  //       final loginModel = LoginModel.fromJson(response.data);
  //       print(
  //         '[AppleSignIn] LoginModel created successfully: ${loginModel.toString()}',
  //       );
  //       developer.log(
  //         '[AppleSignIn] LoginModel parsed: ${loginModel.toString()}',
  //         name: 'Auth',
  //       );
  //       return loginModel;
  //     } else {
  //       print(
  //         '[AppleSignIn] API request failed with status code: ${response.statusCode}, data: ${response.data}',
  //       );
  //       developer.log(
  //         '[AppleSignIn] API error, status: ${response.statusCode}, data: ${response.data}',
  //         name: 'Auth',
  //       );
  //       throw ApiErrorHandler.handleResponseError(
  //         response.statusCode,
  //         response.data,
  //       );
  //     }
  //   } catch (e, stackTrace) {
  //     print('[AppleSignIn] Error during Apple sign-in: $e');
  //     print('[AppleSignIn] Stack trace: $stackTrace');
  //     developer.log(
  //       '[AppleSignIn] Exception occurred: $e',
  //       name: 'Auth',
  //       error: e,
  //       stackTrace: stackTrace,
  //     );
  //     return GoogleAuthResult.failure(e.toString());
  //   }
  // }
}
