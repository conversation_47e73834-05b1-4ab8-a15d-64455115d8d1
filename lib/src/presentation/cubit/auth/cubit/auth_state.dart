part of 'auth_cubit.dart';

@immutable
sealed class AuthState {}

final class AuthInitial extends AuthState {}

final class AuthLoading extends AuthState {
  final bool isLoading;
  AuthLoading(this.isLoading);
  @override
  List<Object> get props => [isLoading];
}

final class AuthSuccess extends AuthState {
  AuthSuccess();
}

final class AuthError extends AuthState {
  final bool invalidCredentials;
  final String error;
  AuthError({required this.error, required this.invalidCredentials});
}

final class AuthGoogleSuccess extends AuthState {
  final String idToken;
  final String? email;
  final String? displayName;
  final String? photoUrl;
  final String? uid;

  AuthGoogleSuccess({
    required this.idToken,
    this.email,
    this.displayName,
    this.photoUrl,
    this.uid,
  });

  List<Object?> get props => [idToken, email, displayName, photoUrl, uid];
}

final class AuthLogout extends AuthState {
  AuthLogout();
}
