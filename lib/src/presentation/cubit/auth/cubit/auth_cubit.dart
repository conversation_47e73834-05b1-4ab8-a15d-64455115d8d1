import 'package:bloc/bloc.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:meta/meta.dart';
import 'package:neorevv/src/data/repository/auth_data_repository.dart';
import '../../../../core/services/exceptions.dart';
import '/src/domain/repository/auth_repository.dart';
import '../../../../domain/models/user.dart';
import '../../../../domain/models/google_auth_result.dart';
import '../../../../core/services/cookie_manager.dart';
part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit(this._authRepository) : super(AuthInitial());

  final AuthRepository _authRepository;

  Future<void> login(Map<String, dynamic> payload) async {
    emit(AuthLoading(true));
    try {
      final result = await _authRepository.login(payload);

      final authRepository = AuthDataRepository();
      authRepository.setTokens(result.jwt, result.refreshToken);

      emit(AuthSuccess());
    } on InvalidCredentialsException catch (e) {
      emit(AuthError(error: e.message, invalidCredentials: true));
    } catch (e) {
      emit(AuthError(error: e.toString(), invalidCredentials: false));
    }
  }

  Future<void> signInWithGoogle() async {
    emit(AuthLoading(true));
    try {
      final result = await _authRepository.signInWithGoogle();

      // Store the Firebase ID token for backend verification
      final authRepository = AuthDataRepository();
      authRepository.setTokens(result.jwt, result.refreshToken!);

      final authRepository1 = AuthDataRepository();

      authRepository1.setTokens(result.jwt, result.refreshToken);

      print('JWT: ${result.jwt}');
      print('Refresh Token: ${result.refreshToken}');

      emit(AuthSuccess());
    } catch (e) {
      emit(AuthError(error: e.toString(), invalidCredentials: false));
    }
  }

  Future<void> logout() async {
    // Clear tokens from AuthDataRepository
    final authRepository = AuthDataRepository();
    authRepository.setTokens('', '');
    // Clear cookies on web platform
    await CookieManager.clearAllCookies();
    // Emit logout state
    emit(AuthLogout());
  }
}
