{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [

        {
            "name": "neorevv-cross-platform-ui (Web - Debug)",
            "request": "launch",
            "type": "dart",
            "args": [
                "-d",
                "web-server",
                "--web-hostname",
                // "************",
                 "neorevv.local",// Change to your desired IP address if not 0.0.0.0 for network access
                "--web-port",
                 "8080" // Change to your desired port
            ]
        },
        {
            "name": "neorevv-cross-platform-ui (Web - Profile)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile",
            "args": [
                "-d",
                "web-server",
                "--web-hostname",
                "************",
                "--web-port",
                "8080"
            ]
        },
        {
            "name": "neorevv-cross-platform-ui (Web - Release)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "-d",
                "web-server",
                "--web-hostname",
                "************",
                // "--web-port",
                // "8080"
            ]
        }
    ]
}
